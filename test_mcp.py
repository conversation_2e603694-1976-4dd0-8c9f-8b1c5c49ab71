#!/usr/bin/env python3
"""
简化的 MCP 测试脚本，不依赖 OpenAI API
"""
import json
import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def test_mcp_server():
    """测试 MCP 服务器连接和工具调用"""
    print("正在连接到 MCP 服务器...")
    
    server_params = StdioServerParameters(
        command="python",
        args=["mcp_server.py"],
        env=None
    )
    
    try:
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                print("正在初始化会话...")
                await session.initialize()
                
                print("正在获取可用工具...")
                tools = await session.list_tools()
                print(f"可用工具: {[tool.name for tool in tools.tools]}")
                
                print("正在调用 get_top_cpu_process 工具...")
                result = await session.call_tool("get_top_cpu_process", {})
                
                # 处理结果
                if hasattr(result, 'content'):
                    if isinstance(result.content, list) and len(result.content) > 0:
                        content = result.content[0].text
                    elif isinstance(result.content, str):
                        content = result.content
                    else:
                        content = str(result.content)
                    
                    print("工具调用结果:")
                    try:
                        parsed_result = json.loads(content)
                        print(json.dumps(parsed_result, indent=2, ensure_ascii=False))
                    except json.JSONDecodeError:
                        print(f"原始结果: {content}")
                else:
                    print(f"结果: {result}")
                    
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    print("=== MCP 服务器测试 ===")
    await test_mcp_server()
    print("=== 测试完成 ===")

if __name__ == "__main__":
    asyncio.run(main())
