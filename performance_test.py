"""
多核异步服务性能测试
测试不同负载下的性能表现，验证多核、异步、线程池的效果
"""

import asyncio
import aiohttp
import time
import json
import statistics
from concurrent.futures import ThreadPoolExecutor
import multiprocessing as mp

# 测试配置
BASE_URL = "http://localhost:8000"
CONCURRENT_REQUESTS = [10, 50, 100, 200]  # 不同并发级别

async def single_request(session, url, data=None):
    """发送单个HTTP请求"""
    try:
        start_time = time.time()
        if data:
            async with session.post(url, json=data) as response:
                result = await response.json()
        else:
            async with session.get(url) as response:
                result = await response.json()
        
        end_time = time.time()
        return {
            "success": True,
            "response_time": end_time - start_time,
            "status": response.status,
            "data": result
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "response_time": 0
        }

async def test_concurrent_requests(url, concurrent_count, data=None):
    """测试并发请求性能"""
    print(f"\n测试 {concurrent_count} 个并发请求到 {url}")
    
    start_time = time.time()
    
    async with aiohttp.ClientSession() as session:
        # 创建并发任务
        tasks = [
            single_request(session, url, data)
            for _ in range(concurrent_count)
        ]
        
        # 执行所有任务
        results = await asyncio.gather(*tasks)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 分析结果
    successful_requests = [r for r in results if r["success"]]
    failed_requests = [r for r in results if not r["success"]]
    
    if successful_requests:
        response_times = [r["response_time"] for r in successful_requests]
        avg_response_time = statistics.mean(response_times)
        median_response_time = statistics.median(response_times)
        min_response_time = min(response_times)
        max_response_time = max(response_times)
    else:
        avg_response_time = median_response_time = min_response_time = max_response_time = 0
    
    # 计算吞吐量
    requests_per_second = len(successful_requests) / total_time if total_time > 0 else 0
    
    print(f"总耗时: {total_time:.2f}秒")
    print(f"成功请求: {len(successful_requests)}/{concurrent_count}")
    print(f"失败请求: {len(failed_requests)}")
    print(f"吞吐量: {requests_per_second:.2f} 请求/秒")
    print(f"平均响应时间: {avg_response_time:.3f}秒")
    print(f"中位数响应时间: {median_response_time:.3f}秒")
    print(f"最小响应时间: {min_response_time:.3f}秒")
    print(f"最大响应时间: {max_response_time:.3f}秒")
    
    return {
        "concurrent_count": concurrent_count,
        "total_time": total_time,
        "successful_requests": len(successful_requests),
        "failed_requests": len(failed_requests),
        "requests_per_second": requests_per_second,
        "avg_response_time": avg_response_time,
        "median_response_time": median_response_time,
        "min_response_time": min_response_time,
        "max_response_time": max_response_time
    }

async def test_single_user_analysis():
    """测试单用户分析端点"""
    print("\n" + "="*60)
    print("测试单用户分析端点 (CPU密集型 + I/O密集型)")
    print("="*60)
    
    url = f"{BASE_URL}/api/users/123/analysis"
    results = []
    
    for concurrent_count in CONCURRENT_REQUESTS:
        result = await test_concurrent_requests(url, concurrent_count)
        results.append(result)
        
        # 等待一下，避免服务器过载
        await asyncio.sleep(2)
    
    return results

async def test_batch_analysis():
    """测试批量用户分析端点"""
    print("\n" + "="*60)
    print("测试批量用户分析端点 (大量CPU密集型任务)")
    print("="*60)
    
    url = f"{BASE_URL}/api/users/batch-analysis"
    # 测试数据：50个用户ID
    test_data = list(range(1, 51))
    results = []
    
    # 对于批量分析，使用较少的并发数
    batch_concurrent = [5, 10, 20, 30]
    
    for concurrent_count in batch_concurrent:
        result = await test_concurrent_requests(url, concurrent_count, test_data)
        results.append(result)
        
        # 批量任务需要更长的等待时间
        await asyncio.sleep(5)
    
    return results

async def test_health_check():
    """测试健康检查端点（轻量级请求）"""
    print("\n" + "="*60)
    print("测试健康检查端点 (轻量级I/O)")
    print("="*60)
    
    url = f"{BASE_URL}/api/health"
    results = []
    
    # 健康检查可以测试更高的并发
    health_concurrent = [50, 100, 200, 500]
    
    for concurrent_count in health_concurrent:
        result = await test_concurrent_requests(url, concurrent_count)
        results.append(result)
        
        await asyncio.sleep(1)
    
    return results

def print_summary(test_name, results):
    """打印测试结果摘要"""
    print(f"\n{test_name} - 性能摘要:")
    print("-" * 80)
    print(f"{'并发数':<10} {'成功率':<10} {'吞吐量(req/s)':<15} {'平均响应时间(s)':<18} {'中位数响应时间(s)':<20}")
    print("-" * 80)
    
    for result in results:
        success_rate = result["successful_requests"] / result["concurrent_count"] * 100
        print(f"{result['concurrent_count']:<10} {success_rate:<9.1f}% {result['requests_per_second']:<14.2f} "
              f"{result['avg_response_time']:<17.3f} {result['median_response_time']:<19.3f}")

async def run_comprehensive_test():
    """运行综合性能测试"""
    print("开始多核异步服务综合性能测试")
    print(f"测试目标: {BASE_URL}")
    print(f"系统CPU核心数: {mp.cpu_count()}")
    
    try:
        # 1. 测试健康检查（验证基础性能）
        health_results = await test_health_check()
        print_summary("健康检查端点", health_results)
        
        # 2. 测试单用户分析（混合负载）
        single_user_results = await test_single_user_analysis()
        print_summary("单用户分析端点", single_user_results)
        
        # 3. 测试批量分析（CPU密集型）
        batch_results = await test_batch_analysis()
        print_summary("批量分析端点", batch_results)
        
        # 4. 生成总结报告
        print("\n" + "="*80)
        print("测试总结")
        print("="*80)
        print("1. 健康检查端点：测试异步I/O处理能力")
        print("2. 单用户分析端点：测试混合负载（I/O + CPU + 外部API）")
        print("3. 批量分析端点：测试CPU密集型任务处理能力")
        print("\n预期观察：")
        print("- 健康检查应该有最高的吞吐量")
        print("- 单用户分析展示了完整的多核异步架构优势")
        print("- 批量分析验证了进程池处理CPU密集型任务的能力")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        print("请确保服务已启动: uvicorn multi_core_async_service:app --host 0.0.0.0 --port 8000")

if __name__ == "__main__":
    print("""
    性能测试工具
    
    使用方法：
    1. 启动服务: uvicorn multi_core_async_service:app --host 0.0.0.0 --port 8000 --workers 4
    2. 运行测试: python performance_test.py
    
    测试内容：
    - 不同并发级别的性能测试
    - CPU密集型 vs I/O密集型任务对比
    - 多核异步架构效果验证
    """)
    
    asyncio.run(run_comprehensive_test())
