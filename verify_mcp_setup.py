#!/usr/bin/env python3
"""
验证 MCP 设置的完整性
"""
import json
import asyncio
import sys
import os
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def test_mcp_server():
    """测试 MCP 服务器"""
    print("🔍 测试 MCP 服务器...")
    
    try:
        server_params = StdioServerParameters(
            command="python",
            args=["mcp_server.py"],
            env=None
        )
        
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                
                # 测试工具列表
                tools = await session.list_tools()
                tool_names = [tool.name for tool in tools.tools]
                
                if "get_top_cpu_process" in tool_names:
                    print("✅ MCP 服务器正常，工具已注册")
                else:
                    print("❌ MCP 服务器工具注册失败")
                    return False
                
                # 测试工具调用
                result = await session.call_tool("get_top_cpu_process", {})
                
                if hasattr(result, 'content'):
                    if isinstance(result.content, list) and len(result.content) > 0:
                        content = json.loads(result.content[0].text)
                    elif isinstance(result.content, str):
                        content = json.loads(result.content)
                    else:
                        content = {"status": "error", "error_message": "Invalid format"}
                    
                    if content.get("status") == "success":
                        print("✅ MCP 工具调用成功")
                        print(f"   找到进程: {content['name']} (PID: {content['pid']}, CPU: {content['cpu_percent']}%)")
                        return True
                    else:
                        print(f"❌ MCP 工具调用失败: {content.get('error_message')}")
                        return False
                        
    except Exception as e:
        print(f"❌ MCP 服务器测试失败: {e}")
        return False

def test_dependencies():
    """测试依赖项"""
    print("🔍 检查依赖项...")
    
    required_modules = [
        ("mcp", "Model Context Protocol"),
        ("psutil", "系统进程监控"),
        ("openai", "OpenAI API 客户端"),
        ("json", "JSON 处理"),
        ("asyncio", "异步IO")
    ]
    
    all_good = True
    for module, description in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
        except ImportError:
            print(f"❌ {module} - {description} (未安装)")
            all_good = False
    
    return all_good

def test_openai_setup():
    """测试 OpenAI 设置"""
    print("🔍 检查 OpenAI API 设置...")
    
    api_key = os.getenv("OPENAI_API_KEY")
    if api_key and api_key != "your-openai-api-key":
        print("✅ OpenAI API Key 已设置")
        return True
    else:
        print("⚠️  OpenAI API Key 未设置 (可选，用于完整客户端功能)")
        print("   设置方法: export OPENAI_API_KEY='your-api-key'")
        return False

async def main():
    """主测试函数"""
    print("=" * 50)
    print("🚀 MCP 设置验证")
    print("=" * 50)
    
    # 测试依赖项
    deps_ok = test_dependencies()
    print()
    
    # 测试 OpenAI 设置
    openai_ok = test_openai_setup()
    print()
    
    # 测试 MCP 服务器
    mcp_ok = await test_mcp_server()
    print()
    
    # 总结
    print("=" * 50)
    print("📋 测试总结")
    print("=" * 50)
    
    if deps_ok and mcp_ok:
        print("🎉 MCP 设置完全正常！")
        print("\n可用的测试脚本:")
        print("  - python test_mcp.py                 # 基础服务器测试")
        print("  - python test_mcp_client_simple.py   # 简化客户端测试")
        print("  - python test_mcp_client_mock.py     # 模拟完整流程")
        
        if openai_ok:
            print("  - python mcp_client.py               # 完整客户端 (需要 OpenAI API)")
        else:
            print("  - 设置 OPENAI_API_KEY 后可使用 mcp_client.py")
            
        return True
    else:
        print("❌ 存在问题，请检查上述错误信息")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
