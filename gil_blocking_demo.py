"""
演示Python GIL如何阻塞CPU密集型任务
即使在线程池中，CPU密集型任务仍然会被GIL串行化
"""

import threading
import time
import concurrent.futures
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor

def cpu_intensive_analysis(data):
    """CPU密集型分析函数 - 会被GIL阻塞"""
    import math
    
    thread_name = threading.current_thread().name
    process_id = mp.current_process().pid
    
    print(f"开始执行 - 线程: {thread_name}, 进程: {process_id}")
    
    # CPU密集型计算
    result = 0
    for i in range(1000000):  # 100万次循环
        # 复杂的数学运算
        result += math.sqrt(i * i + 1)
        if i % 100000 == 0:
            print(f"进度 {i//10000}% - 线程: {thread_name}")
    
    print(f"完成执行 - 线程: {thread_name}, 结果: {result:.2f}")
    return result

def io_intensive_task(task_id):
    """I/O密集型任务 - GIL会释放"""
    thread_name = threading.current_thread().name
    print(f"I/O任务 {task_id} 开始 - 线程: {thread_name}")
    
    # 模拟I/O等待（GIL会在这里释放）
    time.sleep(0.5)
    
    print(f"I/O任务 {task_id} 完成 - 线程: {thread_name}")
    return f"IO-{task_id}-{thread_name}"

def test_cpu_tasks_in_thread_pool():
    """测试：线程池中的CPU密集型任务会被GIL阻塞"""
    print("=" * 60)
    print("测试1: 线程池中的CPU密集型任务（会被GIL阻塞）")
    print("=" * 60)
    
    start_time = time.time()
    
    # 使用线程池执行4个CPU密集型任务
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = []
        for i in range(4):
            future = executor.submit(cpu_intensive_analysis, f"data_{i}")
            futures.append(future)
        
        # 等待所有任务完成
        results = [future.result() for future in futures]
    
    end_time = time.time()
    
    print(f"\n结果: {len(results)} 个任务完成")
    print(f"总耗时: {end_time - start_time:.2f}秒")
    print("观察: 虽然使用了4个线程，但由于GIL，任务基本上是串行执行的！")

def test_io_tasks_in_thread_pool():
    """测试：线程池中的I/O密集型任务（GIL会释放）"""
    print("\n" + "=" * 60)
    print("测试2: 线程池中的I/O密集型任务（GIL会释放）")
    print("=" * 60)
    
    start_time = time.time()
    
    # 使用线程池执行4个I/O密集型任务
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = []
        for i in range(4):
            future = executor.submit(io_intensive_task, i)
            futures.append(future)
        
        # 等待所有任务完成
        results = [future.result() for future in futures]
    
    end_time = time.time()
    
    print(f"\n结果: {results}")
    print(f"总耗时: {end_time - start_time:.2f}秒")
    print("观察: I/O任务可以并发执行，因为在sleep时GIL会释放！")

def test_cpu_tasks_in_process_pool():
    """测试：进程池中的CPU密集型任务（无GIL限制）"""
    print("\n" + "=" * 60)
    print("测试3: 进程池中的CPU密集型任务（无GIL限制）")
    print("=" * 60)
    
    start_time = time.time()
    
    # 使用进程池执行4个CPU密集型任务
    with ProcessPoolExecutor(max_workers=4) as executor:
        futures = []
        for i in range(4):
            future = executor.submit(cpu_intensive_analysis, f"data_{i}")
            futures.append(future)
        
        # 等待所有任务完成
        results = [future.result() for future in futures]
    
    end_time = time.time()
    
    print(f"\n结果: {len(results)} 个任务完成")
    print(f"总耗时: {end_time - start_time:.2f}秒")
    print("观察: 进程池中的任务可以真正并行执行！")

def test_mixed_workload_thread_pool():
    """测试：混合负载在线程池中的表现"""
    print("\n" + "=" * 60)
    print("测试4: 混合负载（CPU + I/O）在线程池中的表现")
    print("=" * 60)
    
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=6) as executor:
        futures = []
        
        # 提交2个CPU密集型任务
        for i in range(2):
            future = executor.submit(cpu_intensive_analysis, f"cpu_data_{i}")
            futures.append(future)
        
        # 提交4个I/O密集型任务
        for i in range(4):
            future = executor.submit(io_intensive_task, i)
            futures.append(future)
        
        # 等待所有任务完成
        results = [future.result() for future in futures]
    
    end_time = time.time()
    
    print(f"\n总耗时: {end_time - start_time:.2f}秒")
    print("观察: CPU任务会阻塞其他CPU任务，但I/O任务仍可以并发执行！")

def demonstrate_gil_behavior():
    """演示GIL的具体行为"""
    print("\n" + "=" * 60)
    print("GIL行为演示")
    print("=" * 60)
    
    def cpu_with_prints():
        """带打印的CPU任务，观察执行顺序"""
        thread_name = threading.current_thread().name
        for i in range(5):
            # CPU密集型计算
            result = sum(j * j for j in range(100000))
            print(f"线程 {thread_name}: 完成计算 {i+1}, 结果: {result}")
            # 注意：这里没有time.sleep()，纯CPU计算
    
    print("启动3个线程执行CPU密集型任务...")
    threads = []
    for i in range(3):
        t = threading.Thread(target=cpu_with_prints, name=f"Thread-{i+1}")
        threads.append(t)
        t.start()
    
    for t in threads:
        t.join()
    
    print("\n观察结果：")
    print("- 虽然有3个线程，但输出基本是串行的")
    print("- 这证明了GIL让CPU密集型任务无法真正并行")

def main():
    """主测试函数"""
    print("Python GIL阻塞演示")
    print("这个演示将证明：即使在线程池中，CPU密集型任务仍会被GIL串行化")
    
    # 测试1: 线程池中的CPU任务
    test_cpu_tasks_in_thread_pool()
    
    # 测试2: 线程池中的I/O任务
    test_io_tasks_in_thread_pool()
    
    # 测试3: 进程池中的CPU任务
    test_cpu_tasks_in_process_pool()
    
    # 测试4: 混合负载
    test_mixed_workload_thread_pool()
    
    # 演示GIL行为
    demonstrate_gil_behavior()
    
    print("\n" + "=" * 60)
    print("总结:")
    print("1. 线程池中的CPU密集型任务仍然被GIL串行化")
    print("2. 线程池中的I/O密集型任务可以并发执行")
    print("3. 只有进程池才能真正并行执行CPU任务")
    print("4. 这就是为什么Python需要多进程来利用多核CPU")
    print("=" * 60)

if __name__ == "__main__":
    main()
