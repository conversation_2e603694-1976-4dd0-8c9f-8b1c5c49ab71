#!/usr/bin/env python3
"""
模拟 OpenAI API 的 MCP 客户端测试
"""
import json
import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# 模拟 OpenAI 响应
class MockOpenAIResponse:
    def __init__(self):
        self.choices = [MockChoice()]

class MockChoice:
    def __init__(self):
        self.message = MockMessage()

class MockMessage:
    def __init__(self):
        self.tool_calls = [MockToolCall()]
        self.content = None

class MockToolCall:
    def __init__(self):
        self.function = MockFunction()

class MockFunction:
    def __init__(self):
        self.name = "get_top_cpu_process"
        self.arguments = "{}"

# 连接到 MCP 服务器并调用工具
async def call_mcp_tool(tool_name: str, arguments: dict) -> dict:
    server_params = StdioServerParameters(
        command="python",
        args=["mcp_server.py"],
        env=None
    )
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            result = await session.call_tool(tool_name, arguments)
            # 修复结果处理
            if hasattr(result, 'content'):
                if isinstance(result.content, list) and len(result.content) > 0:
                    return json.loads(result.content[0].text)
                elif isinstance(result.content, str):
                    return json.loads(result.content)
            return {"status": "error", "error_message": "Invalid result format"}

# 模拟与 OpenAI API 交互
async def run_conversation_mock(user_input: str) -> dict:
    print(f"用户输入: {user_input}")
    
    # 模拟 OpenAI 决定调用工具
    print("模拟 OpenAI API 响应: 需要调用 get_top_cpu_process 工具")
    
    # 模拟工具调用
    function_name = "get_top_cpu_process"
    function_args = {}
    
    if function_name == "get_top_cpu_process":
        result = await call_mcp_tool(function_name, function_args)
        return result
    else:
        return {"status": "error", "error_message": "Unknown function"}

# 示例使用
async def main():
    print("=== 模拟完整 MCP 客户端流程 ===")
    user_input = "Find the process using the most CPU on my computer"
    result = await run_conversation_mock(user_input)
    
    print("\n最终结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    if result.get("status") == "success":
        print(f"\n🎉 成功找到 CPU 使用率最高的进程:")
        print(f"   进程名: {result['name']}")
        print(f"   进程ID: {result['pid']}")
        print(f"   CPU使用率: {result['cpu_percent']}%")

if __name__ == "__main__":
    asyncio.run(main())
