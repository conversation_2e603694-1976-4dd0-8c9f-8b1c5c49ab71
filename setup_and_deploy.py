"""
多核异步服务部署和设置脚本
包含数据库初始化、依赖安装和性能优化配置
"""

import asyncio
import asyncpg
import subprocess
import sys
import os
import multiprocessing as mp

# 数据库配置
DATABASE_URL = "postgresql://user:password@localhost:5432/testdb"

async def create_test_database():
    """创建测试数据库和表"""
    try:
        # 连接到默认数据库创建测试数据库
        conn = await asyncpg.connect("postgresql://user:password@localhost:5432/postgres")
        
        # 创建测试数据库
        try:
            await conn.execute("CREATE DATABASE testdb")
            print("✓ 测试数据库创建成功")
        except Exception as e:
            if "already exists" in str(e):
                print("✓ 测试数据库已存在")
            else:
                raise e
        
        await conn.close()
        
        # 连接到测试数据库创建表
        conn = await asyncpg.connect(DATABASE_URL)
        
        # 创建用户表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                value INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建交易表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS transactions (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id),
                amount DECIMAL(10,2) NOT NULL,
                type VARCHAR(20) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 插入测试数据
        print("插入测试数据...")
        
        # 插入用户数据
        for i in range(1, 1001):  # 1000个用户
            await conn.execute(
                "INSERT INTO users (name, value) VALUES ($1, $2) ON CONFLICT DO NOTHING",
                f"User{i}", i * 10 + 50
            )
        
        # 插入交易数据
        for user_id in range(1, 101):  # 前100个用户有交易记录
            for j in range(1, 21):  # 每个用户20条交易
                await conn.execute(
                    "INSERT INTO transactions (user_id, amount, type) VALUES ($1, $2, $3)",
                    user_id, (j * 100) % 1000, "credit" if j % 2 == 0 else "debit"
                )
        
        # 创建索引优化查询性能
        await conn.execute("CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id)")
        await conn.execute("CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at)")
        
        await conn.close()
        print("✓ 数据库表和测试数据创建成功")
        
    except Exception as e:
        print(f"✗ 数据库设置失败: {e}")
        print("请确保PostgreSQL已安装并运行，用户名密码正确")

def install_dependencies():
    """安装必要的Python依赖"""
    dependencies = [
        "fastapi",
        "uvicorn[standard]",
        "asyncpg",
        "aiohttp",
        "requests",
        "psutil"  # 用于系统监控
    ]
    
    print("安装Python依赖...")
    for dep in dependencies:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print(f"✓ {dep} 安装成功")
        except subprocess.CalledProcessError:
            print(f"✗ {dep} 安装失败")

def check_system_resources():
    """检查系统资源"""
    print("\n系统资源检查:")
    print(f"CPU核心数: {mp.cpu_count()}")
    
    try:
        import psutil
        print(f"内存总量: {psutil.virtual_memory().total / (1024**3):.1f} GB")
        print(f"可用内存: {psutil.virtual_memory().available / (1024**3):.1f} GB")
        print(f"CPU使用率: {psutil.cpu_percent(interval=1):.1f}%")
    except ImportError:
        print("安装psutil以获取详细系统信息: pip install psutil")

def generate_deployment_configs():
    """生成部署配置文件"""
    
    # Gunicorn配置（生产环境推荐）
    gunicorn_config = f"""
# gunicorn_config.py
import multiprocessing

# 服务器配置
bind = "0.0.0.0:8000"
workers = {mp.cpu_count()}  # 每个CPU核心一个工作进程
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000

# 性能优化
max_requests = 1000  # 每个worker处理1000个请求后重启
max_requests_jitter = 100
preload_app = True
keepalive = 5

# 日志配置
accesslog = "access.log"
errorlog = "error.log"
loglevel = "info"

# 进程管理
timeout = 120
graceful_timeout = 30
"""

    # Docker配置
    dockerfile = f"""
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["gunicorn", "-c", "gunicorn_config.py", "multi_core_async_service:app"]
"""

    # requirements.txt
    requirements = """
fastapi==0.104.1
uvicorn[standard]==0.24.0
asyncpg==0.29.0
aiohttp==3.9.1
requests==2.31.0
gunicorn==21.2.0
psutil==5.9.6
"""

    # 写入配置文件
    with open("gunicorn_config.py", "w") as f:
        f.write(gunicorn_config)
    
    with open("Dockerfile", "w") as f:
        f.write(dockerfile)
    
    with open("requirements.txt", "w") as f:
        f.write(requirements)
    
    print("✓ 部署配置文件生成成功")

def print_deployment_instructions():
    """打印部署说明"""
    print(f"""
{'='*80}
多核异步HTTP数据库服务部署指南
{'='*80}

1. 开发环境启动:
   uvicorn multi_core_async_service:app --host 0.0.0.0 --port 8000 --workers {mp.cpu_count()}

2. 生产环境启动 (推荐):
   gunicorn -c gunicorn_config.py multi_core_async_service:app

3. Docker部署:
   docker build -t multi-core-async-service .
   docker run -p 8000:8000 multi-core-async-service

4. 性能测试:
   python performance_test.py

5. 监控端点:
   - 健康检查: GET /api/health
   - 单用户分析: GET /api/users/{{user_id}}/analysis
   - 批量分析: POST /api/users/batch-analysis

架构优势:
✓ 多进程: 充分利用多核CPU ({mp.cpu_count()} 核心)
✓ 异步I/O: 高效处理数据库和网络请求
✓ 线程池: 处理阻塞的第三方API调用
✓ 进程池: CPU密集型任务并行处理

预期性能:
- 健康检查: >1000 req/s
- 单用户分析: 100-500 req/s (取决于外部API延迟)
- 批量分析: 10-50 req/s (CPU密集型任务)

注意事项:
1. 确保PostgreSQL已安装并运行
2. 根据实际负载调整worker数量和连接池大小
3. 监控内存使用，避免进程池任务过多导致内存不足
4. 生产环境建议使用Redis等缓存减少数据库压力
""")

async def main():
    """主设置流程"""
    print("开始设置多核异步HTTP数据库服务...")
    
    # 1. 检查系统资源
    check_system_resources()
    
    # 2. 安装依赖
    install_dependencies()
    
    # 3. 设置数据库
    await create_test_database()
    
    # 4. 生成配置文件
    generate_deployment_configs()
    
    # 5. 打印部署说明
    print_deployment_instructions()

if __name__ == "__main__":
    asyncio.run(main())
