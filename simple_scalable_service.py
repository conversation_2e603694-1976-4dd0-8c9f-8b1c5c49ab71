"""
简化的可扩展服务架构
Flask + 线程池 + 异步I/O，通过K8s水平扩展
避免复杂的多进程架构，简化部署和管理
"""

from flask import Flask, request, jsonify
import asyncio
import aiohttp
import asyncpg
import threading
import time
import os
from concurrent.futures import ThreadPoolExecutor
import queue
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 全局资源
thread_pool = ThreadPoolExecutor(max_workers=20)
db_pool = None
async_loop = None
loop_thread = None

# ============= 异步事件循环管理 =============
def run_async_loop():
    """在独立线程中运行异步事件循环"""
    global async_loop
    async_loop = asyncio.new_event_loop()
    asyncio.set_event_loop(async_loop)
    async_loop.run_forever()

def run_async_task(coro):
    """在异步循环中执行协程"""
    if async_loop is None:
        raise RuntimeError("Async loop not initialized")
    
    future = asyncio.run_coroutine_threadsafe(coro, async_loop)
    return future.result()

# ============= 异步数据库操作 =============
async def init_db_pool():
    """初始化数据库连接池"""
    global db_pool
    try:
        db_pool = await asyncpg.create_pool(
            "postgresql://user:password@localhost:5432/testdb",
            min_size=5,
            max_size=10
        )
        logger.info("数据库连接池初始化成功")
    except Exception as e:
        logger.warning(f"数据库连接失败，使用模拟数据: {e}")
        db_pool = None

async def get_user_data_async(user_id: int):
    """异步获取用户数据"""
    if db_pool:
        async with db_pool.acquire() as conn:
            query = "SELECT id, name, email, created_at FROM users WHERE id = $1"
            row = await conn.fetchrow(query, user_id)
            return dict(row) if row else None
    else:
        # 模拟数据库延迟
        await asyncio.sleep(0.05)
        return {
            "id": user_id,
            "name": f"User{user_id}",
            "email": f"user{user_id}@example.com",
            "created_at": "2024-01-01T00:00:00"
        }

async def get_user_transactions_async(user_id: int):
    """异步获取用户交易记录"""
    if db_pool:
        async with db_pool.acquire() as conn:
            query = "SELECT * FROM transactions WHERE user_id = $1 ORDER BY created_at DESC LIMIT 50"
            rows = await conn.fetch(query, user_id)
            return [dict(row) for row in rows]
    else:
        await asyncio.sleep(0.03)
        return [
            {
                "id": i,
                "user_id": user_id,
                "amount": (i * 100) % 1000,
                "type": "credit" if i % 2 == 0 else "debit",
                "created_at": "2024-01-01T00:00:00"
            }
            for i in range(1, 11)
        ]

async def call_external_api_async(user_id: int):
    """异步调用外部API"""
    try:
        async with aiohttp.ClientSession() as session:
            # 模拟外部API调用
            await asyncio.sleep(0.1)  # 模拟网络延迟
            return {
                "user_id": user_id,
                "external_score": (user_id * 123) % 1000,
                "api_version": "v1.0",
                "timestamp": time.time()
            }
    except Exception as e:
        logger.error(f"外部API调用失败: {e}")
        return {"error": str(e)}

# ============= CPU密集型任务（在线程池中执行）=============
def cpu_intensive_analysis(data):
    """CPU密集型数据分析"""
    import statistics
    import math
    
    logger.info(f"CPU分析任务开始，线程: {threading.current_thread().name}")
    
    # 模拟复杂计算
    values = []
    for record in data:
        if 'amount' in record:
            val = record['amount']
            # 增加计算复杂度
            for _ in range(1000):
                val = math.sqrt(val * val + 1)
            values.append(val)
    
    if not values:
        return {"error": "No data to analyze"}
    
    result = {
        "count": len(values),
        "mean": statistics.mean(values),
        "median": statistics.median(values),
        "std_dev": statistics.stdev(values) if len(values) > 1 else 0,
        "processed_by_thread": threading.current_thread().name,
        "process_id": os.getpid()
    }
    
    logger.info(f"CPU分析任务完成，线程: {threading.current_thread().name}")
    return result

# ============= Flask路由 =============
@app.route('/health')
def health_check():
    """健康检查"""
    return jsonify({
        "status": "healthy",
        "process_id": os.getpid(),
        "thread_count": threading.active_count(),
        "timestamp": time.time()
    })

@app.route('/api/users/<int:user_id>')
def get_user(user_id):
    """获取用户基本信息"""
    try:
        # 在异步循环中执行I/O操作
        user_data = run_async_task(get_user_data_async(user_id))
        
        if not user_data:
            return jsonify({"error": "User not found"}), 404
        
        return jsonify({
            "user": user_data,
            "process_id": os.getpid()
        })
    
    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/users/<int:user_id>/analysis')
def analyze_user(user_id):
    """用户数据分析 - 混合I/O和CPU任务"""
    start_time = time.time()
    
    try:
        # 1. 并发执行多个异步I/O操作
        async def gather_data():
            user_task = get_user_data_async(user_id)
            transactions_task = get_user_transactions_async(user_id)
            external_task = call_external_api_async(user_id)
            
            return await asyncio.gather(user_task, transactions_task, external_task)
        
        user_data, transactions, external_data = run_async_task(gather_data())
        
        # 2. 在线程池中执行CPU密集型分析
        analysis_future = thread_pool.submit(cpu_intensive_analysis, transactions)
        analysis_result = analysis_future.result()
        
        end_time = time.time()
        
        return jsonify({
            "user_data": user_data,
            "transaction_analysis": analysis_result,
            "external_data": external_data,
            "processing_time": f"{end_time - start_time:.3f}s",
            "process_id": os.getpid(),
            "architecture": "flask + thread_pool + async_io"
        })
        
    except Exception as e:
        logger.error(f"用户分析失败: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/users/batch', methods=['POST'])
def batch_analyze_users():
    """批量用户分析"""
    try:
        user_ids = request.json.get('user_ids', [])
        if len(user_ids) > 50:
            return jsonify({"error": "最多支持50个用户"}), 400
        
        start_time = time.time()
        
        # 并发处理多个用户
        def process_single_user(user_id):
            try:
                # 异步获取数据
                async def get_user_all_data():
                    user_task = get_user_data_async(user_id)
                    transactions_task = get_user_transactions_async(user_id)
                    return await asyncio.gather(user_task, transactions_task)
                
                user_data, transactions = run_async_task(get_user_all_data())
                
                # CPU分析
                analysis = cpu_intensive_analysis(transactions)
                
                return {
                    "user_id": user_id,
                    "user_data": user_data,
                    "analysis": analysis
                }
            except Exception as e:
                return {"user_id": user_id, "error": str(e)}
        
        # 在线程池中并发处理所有用户
        futures = [thread_pool.submit(process_single_user, uid) for uid in user_ids]
        results = [f.result() for f in futures]
        
        end_time = time.time()
        
        return jsonify({
            "results": results,
            "total_users": len(user_ids),
            "processing_time": f"{end_time - start_time:.3f}s",
            "process_id": os.getpid()
        })
        
    except Exception as e:
        logger.error(f"批量分析失败: {e}")
        return jsonify({"error": str(e)}), 500

# ============= 应用初始化 =============
def init_app():
    """初始化应用"""
    global loop_thread
    
    # 启动异步事件循环线程
    loop_thread = threading.Thread(target=run_async_loop, daemon=True)
    loop_thread.start()
    
    # 等待事件循环启动
    time.sleep(0.1)
    
    # 初始化数据库连接池
    run_async_task(init_db_pool())
    
    logger.info(f"应用初始化完成，进程ID: {os.getpid()}")

# ============= 应用启动 =============
if __name__ == '__main__':
    init_app()

    print(f"""
    简化可扩展服务启动

    架构特点：
    1. 单进程应用，避免复杂的多进程管理
    2. 异步I/O处理数据库和网络请求
    3. 线程池处理CPU密集型任务
    4. 通过K8s水平扩展实现多核利用

    进程ID: {os.getpid()}
    线程池大小: 20
    端口: {os.environ.get('PORT', 5000)}

    测试端点：
    - GET /health - 健康检查
    - GET /api/users/<id> - 获取用户信息
    - GET /api/users/<id>/analysis - 用户分析
    - POST /api/users/batch - 批量分析
    """)

    # 生产环境建议使用gunicorn
    # gunicorn -w 1 -b 0.0.0.0:5000 --timeout 120 simple_scalable_service:app
    app.run(
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 5000)),
        debug=False,
        threaded=True
    )
