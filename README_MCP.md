# MCP 服务器和客户端使用说明

这个项目演示了如何使用 Model Context Protocol (MCP) 创建一个 CPU 监控服务。

## 文件说明

- `mcp_server.py` - MCP 服务器，提供 CPU 监控工具
- `mcp_client.py` - 完整的 MCP 客户端，集成 OpenAI API
- `test_mcp.py` - 基础 MCP 服务器测试
- `test_mcp_client_simple.py` - 简化的客户端测试（不需要 OpenAI API）

## 快速开始

### 1. 测试 MCP 服务器

```bash
python test_mcp.py
```

这将测试 MCP 服务器的基本功能，包括：
- 连接到服务器
- 列出可用工具
- 调用 `get_top_cpu_process` 工具

### 2. 测试简化客户端

```bash
python test_mcp_client_simple.py
```

这将模拟客户端请求，直接调用 MCP 工具获取 CPU 使用率最高的进程。

### 3. 使用完整客户端（需要 OpenAI API）

1. 设置环境变量：
   ```bash
   export OPENAI_API_KEY="your-openai-api-key"
   ```

2. 运行客户端：
   ```bash
   python mcp_client.py
   ```

## 功能特性

### MCP 服务器 (`mcp_server.py`)
- 提供 `get_top_cpu_process` 工具
- 返回当前 CPU 使用率最高的进程信息
- 包含进程名、PID 和 CPU 使用率
- 优化了性能和错误处理

### MCP 客户端 (`mcp_client.py`)
- 集成 OpenAI GPT-3.5-turbo
- 支持自然语言查询
- 自动调用相应的 MCP 工具
- 使用最新的 OpenAI API

## 修复的问题

1. **参数命名问题**: 修复了 MCP 工具函数中不能使用下划线开头的参数名
2. **OpenAI API 更新**: 更新到最新的 OpenAI 客户端 API
3. **路径问题**: 修正了客户端中的服务器路径
4. **结果处理**: 改进了 MCP 工具调用结果的处理逻辑
5. **性能优化**: 优化了 CPU 监控的性能和错误处理

## 示例输出

```json
{
  "status": "success",
  "name": "Microsoft Edge Helper (GPU)",
  "pid": 7312,
  "cpu_percent": 23.4
}
```

## 依赖项

项目依赖已在 `pyproject.toml` 中定义：
- `mcp[cli]` - Model Context Protocol
- `openai` - OpenAI API 客户端
- `psutil` - 系统进程监控
- `httpx` - HTTP 客户端

## 故障排除

如果遇到连接问题：
1. 确保所有依赖都已安装
2. 检查 Python 版本（需要 >= 3.11）
3. 确保 MCP 服务器路径正确
4. 查看错误日志获取详细信息
