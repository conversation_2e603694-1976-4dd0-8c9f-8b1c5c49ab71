"""
多核异步HTTP数据库服务示例
结合多进程、线程池和异步I/O来充分利用系统资源

架构设计：
1. 多进程：利用多核CPU处理CPU密集型任务
2. 异步I/O：高效处理数据库连接和HTTP请求
3. 线程池：处理阻塞的第三方库调用
"""

import asyncio
import asyncpg
import aiohttp
from fastapi import FastAPI, HTTPException
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import multiprocessing as mp
import threading
import time
import json
import os
from typing import List, Dict, Any
import uvicorn

# ============= 数据库配置 =============
DATABASE_URL = "postgresql://user:password@localhost:5432/testdb"

# ============= CPU密集型任务（在独立进程中执行）=============
def cpu_intensive_analysis(data: List[Dict]) -> Dict[str, Any]:
    """
    CPU密集型数据分析任务
    这个函数会在独立的进程中执行，充分利用多核CPU
    """
    import statistics
    import math
    
    print(f"CPU分析任务运行在进程 {os.getpid()}")
    
    # 模拟复杂的数据分析
    values = []
    for record in data:
        if 'value' in record:
            # 模拟复杂计算
            val = record['value']
            for _ in range(1000):  # 增加CPU负载
                val = math.sqrt(val * val + 1)
            values.append(val)
    
    if not values:
        return {"error": "No valid data"}
    
    return {
        "count": len(values),
        "mean": statistics.mean(values),
        "median": statistics.median(values),
        "std_dev": statistics.stdev(values) if len(values) > 1 else 0,
        "min": min(values),
        "max": max(values),
        "processed_by_pid": os.getpid()
    }

def cpu_intensive_aggregation(user_ids: List[int]) -> Dict[str, Any]:
    """
    另一个CPU密集型任务：用户数据聚合
    """
    print(f"聚合任务运行在进程 {os.getpid()}")
    
    # 模拟复杂的聚合计算
    result = {}
    for user_id in user_ids:
        # 模拟复杂计算
        score = 0
        for i in range(10000):  # CPU密集型循环
            score += (user_id * i) % 1000
        
        result[f"user_{user_id}"] = {
            "score": score,
            "rank": score % 100,
            "category": "A" if score > 500000 else "B"
        }
    
    return {
        "aggregated_data": result,
        "total_users": len(user_ids),
        "processed_by_pid": os.getpid()
    }

# ============= FastAPI应用 =============
app = FastAPI(title="多核异步数据库服务")

# 全局资源池
process_pool = None
thread_pool = None
db_pool = None

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化资源池"""
    global process_pool, thread_pool, db_pool
    
    # 创建进程池（利用多核CPU）
    cpu_count = mp.cpu_count()
    process_pool = ProcessPoolExecutor(max_workers=cpu_count)
    print(f"创建进程池，工作进程数: {cpu_count}")
    
    # 创建线程池（处理阻塞调用）
    thread_pool = ThreadPoolExecutor(max_workers=20)
    print(f"创建线程池，工作线程数: 20")
    
    # 创建异步数据库连接池
    try:
        db_pool = await asyncpg.create_pool(
            DATABASE_URL,
            min_size=10,
            max_size=20,
            command_timeout=60
        )
        print("数据库连接池创建成功")
    except Exception as e:
        print(f"数据库连接失败: {e}")
        # 使用模拟数据库
        db_pool = None

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理资源"""
    global process_pool, thread_pool, db_pool
    
    if process_pool:
        process_pool.shutdown(wait=True)
    if thread_pool:
        thread_pool.shutdown(wait=True)
    if db_pool:
        await db_pool.close()

# ============= 模拟数据库操作 =============
async def get_user_data_from_db(user_ids: List[int]) -> List[Dict]:
    """异步数据库查询"""
    if db_pool:
        # 真实数据库查询
        async with db_pool.acquire() as conn:
            query = "SELECT id, name, value FROM users WHERE id = ANY($1)"
            rows = await conn.fetch(query, user_ids)
            return [dict(row) for row in rows]
    else:
        # 模拟数据库查询延迟
        await asyncio.sleep(0.1)
        return [
            {"id": uid, "name": f"User{uid}", "value": uid * 10 + 50}
            for uid in user_ids
        ]

async def get_transaction_data(user_id: int) -> List[Dict]:
    """异步查询交易数据"""
    if db_pool:
        async with db_pool.acquire() as conn:
            query = "SELECT * FROM transactions WHERE user_id = $1 ORDER BY created_at DESC LIMIT 100"
            rows = await conn.fetch(query, user_id)
            return [dict(row) for row in rows]
    else:
        # 模拟查询延迟
        await asyncio.sleep(0.05)
        return [
            {
                "id": i,
                "user_id": user_id,
                "amount": (i * 100) % 1000,
                "type": "credit" if i % 2 == 0 else "debit"
            }
            for i in range(1, 21)  # 返回20条模拟交易
        ]

# ============= 阻塞的第三方API调用（在线程池中执行）=============
def call_external_api(user_id: int) -> Dict:
    """
    模拟调用不支持异步的第三方API
    这种阻塞调用放在线程池中执行
    """
    import requests
    import time
    
    print(f"外部API调用运行在线程 {threading.current_thread().name}")
    
    # 模拟网络延迟
    time.sleep(0.2)
    
    # 模拟API响应
    return {
        "user_id": user_id,
        "external_score": (user_id * 123) % 1000,
        "api_version": "v1.0",
        "called_by_thread": threading.current_thread().name
    }

# ============= API端点 =============

@app.get("/api/users/{user_id}/analysis")
async def analyze_user_data(user_id: int):
    """
    综合示例：分析单个用户数据
    结合异步I/O、线程池和进程池
    """
    start_time = time.time()
    
    try:
        # 1. 异步并发查询数据库
        user_data_task = get_user_data_from_db([user_id])
        transaction_data_task = get_transaction_data(user_id)
        
        # 2. 在线程池中调用外部API（阻塞调用）
        loop = asyncio.get_event_loop()
        external_api_task = loop.run_in_executor(
            thread_pool, call_external_api, user_id
        )
        
        # 等待所有I/O操作完成
        user_data, transaction_data, external_data = await asyncio.gather(
            user_data_task,
            transaction_data_task,
            external_api_task
        )
        
        # 3. 在进程池中执行CPU密集型分析
        analysis_task = loop.run_in_executor(
            process_pool, cpu_intensive_analysis, transaction_data
        )
        
        analysis_result = await analysis_task
        
        end_time = time.time()
        
        return {
            "user_data": user_data,
            "transaction_analysis": analysis_result,
            "external_data": external_data,
            "processing_time": f"{end_time - start_time:.3f}s",
            "main_process_pid": os.getpid(),
            "architecture": "async_io + thread_pool + process_pool"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/users/batch-analysis")
async def batch_analyze_users(user_ids: List[int]):
    """
    批量用户分析：展示如何处理大量并发请求
    """
    if len(user_ids) > 100:
        raise HTTPException(status_code=400, detail="最多支持100个用户")
    
    start_time = time.time()
    
    try:
        # 1. 异步批量查询数据库
        user_data = await get_user_data_from_db(user_ids)
        
        # 2. 在进程池中执行CPU密集型聚合
        loop = asyncio.get_event_loop()
        aggregation_task = loop.run_in_executor(
            process_pool, cpu_intensive_aggregation, user_ids
        )
        
        # 3. 并发调用外部API（使用线程池）
        external_tasks = [
            loop.run_in_executor(thread_pool, call_external_api, uid)
            for uid in user_ids[:10]  # 限制外部API调用数量
        ]
        
        # 等待所有任务完成
        aggregation_result, *external_results = await asyncio.gather(
            aggregation_task,
            *external_tasks
        )
        
        end_time = time.time()
        
        return {
            "user_count": len(user_ids),
            "user_data": user_data,
            "aggregation_result": aggregation_result,
            "external_api_samples": external_results,
            "processing_time": f"{end_time - start_time:.3f}s",
            "main_process_pid": os.getpid()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "process_id": os.getpid(),
        "thread_count": threading.active_count(),
        "cpu_count": mp.cpu_count()
    }

# ============= 启动配置 =============
if __name__ == "__main__":
    print(f"""
    多核异步HTTP数据库服务启动

    架构说明：
    1. 主进程数量: {mp.cpu_count()} (每个CPU核心一个)
    2. 每个进程内的线程池: 20个线程处理阻塞调用
    3. 每个进程内的进程池: {mp.cpu_count()}个子进程处理CPU密集型任务
    4. 异步I/O: 处理数据库和HTTP连接

    测试端点：
    - GET /api/users/123/analysis - 单用户分析
    - POST /api/users/batch-analysis - 批量用户分析
    - GET /api/health - 健康检查

    启动命令：
    uvicorn multi_core_async_service:app --host 0.0.0.0 --port 8000 --workers {mp.cpu_count()}
    """)

    # 使用多进程启动（每个CPU核心一个工作进程）
    uvicorn.run(
        "multi_core_async_service:app",
        host="0.0.0.0",
        port=8000,
        workers=mp.cpu_count(),  # 每个CPU核心一个工作进程
        reload=False
    )
