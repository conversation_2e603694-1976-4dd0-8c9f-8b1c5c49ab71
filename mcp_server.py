from mcp.server.fastmcp import FastMCP
import psutil
import json

# 初始化 FastMCP 服务器
mcp = FastMCP("cpu-monitor")

# 定义工具：查询当前使用 CPU 最多的进程
@mcp.tool()
def get_top_cpu_process() -> str:
    """Get the process with the highest CPU usage on the local machine.
    Returns:
        A JSON string containing the process name, PID, and CPU usage percentage.
    """
    try:
        # 获取所有进程信息，优化性能
        processes = []
        for proc in psutil.process_iter(['name', 'pid']):
            try:
                # 获取CPU使用率，使用较短的间隔以提高响应速度
                cpu_percent = proc.cpu_percent(interval=0.05)
                if cpu_percent > 0:
                    processes.append((proc.info['name'], proc.info['pid'], cpu_percent))
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                # 忽略无法访问的进程
                continue

        if not processes:
            return json.dumps({"status": "error", "message": "No active processes found"})

        # 找到 CPU 使用率最高的进程
        top_process = max(processes, key=lambda x: x[2])
        return json.dumps({
            "status": "success",
            "name": top_process[0],
            "pid": top_process[1],
            "cpu_percent": round(top_process[2], 2)
        })
    except Exception as e:
        return json.dumps({"status": "error", "error_message": str(e)})

# 启动服务器
if __name__ == "__main__":
    mcp.run()
