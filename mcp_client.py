import os
import json
import asyncio
from openai import OpenAI
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# 设置 OpenAI 客户端
client = OpenAI(
    api_key=os.getenv("OPENAI_API_KEY", "your-openai-api-key")
)

# 定义 OpenAI 函数描述
functions = [
    {
        "name": "get_top_cpu_process",
        "description": "Get the process with the highest CPU usage on the local machine",
        "parameters": {
            "type": "object",
            "properties": {},
            "required": []
        }
    }
]

# 连接到 MCP 服务器并调用工具
async def call_mcp_tool(tool_name: str, arguments: dict) -> dict:
    server_params = StdioServerParameters(
        command="python",
        args=["mcp_server.py"],
        env=None
    )
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            result = await session.call_tool(tool_name, arguments)
            # 修复结果处理
            if hasattr(result, 'content'):
                if isinstance(result.content, list) and len(result.content) > 0:
                    return json.loads(result.content[0].text)
                elif isinstance(result.content, str):
                    return json.loads(result.content)
            return {"status": "error", "error_message": "Invalid result format"}

# 与 OpenAI API 交互
async def run_conversation(user_input: str) -> dict:
    messages = [{"role": "user", "content": user_input}]

    # 调用 OpenAI API (使用新的客户端API)
    response = client.chat.completions.create(
        model="gpt-3.5-turbo",
        messages=messages,
        tools=[{
            "type": "function",
            "function": functions[0]
        }],
        tool_choice="auto"
    )

    response_message = response.choices[0].message

    # 检查是否需要调用函数
    if response_message.tool_calls:
        tool_call = response_message.tool_calls[0]
        function_name = tool_call.function.name
        function_args = json.loads(tool_call.function.arguments)

        if function_name == "get_top_cpu_process":
            result = await call_mcp_tool(function_name, function_args)
            return result
        else:
            return {"status": "error", "error_message": "Unknown function"}

    return {"status": "success", "message": response_message.content}

# 示例使用
async def main():
    user_input = "Find the process using the most CPU on my computer"
    result = await run_conversation(user_input)
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    asyncio.run(main())
