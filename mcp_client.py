"""
异步编程示例 - MCP 客户端

async/await 关键词说明：
- async: 用于声明异步函数，表示该函数可能包含异步操作
- await: 用于等待异步操作完成，在等待期间不会阻塞整个程序
- 异步编程的优势：在等待 I/O 操作（如网络请求、文件读写）时，
  程序可以继续执行其他任务，提高效率和响应性

本文件展示了如何使用异步编程与 MCP 服务器和 OpenAI API 进行交互
"""

import os
import json
import asyncio
from openai import OpenAI
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# 设置 OpenAI 客户端
client = OpenAI(
    api_key=os.getenv("OPENAI_API_KEY", "your-openai-api-key")
)

# 定义 OpenAI 函数描述
functions = [
    {
        "name": "get_top_cpu_process",
        "description": "Get the process with the highest CPU usage on the local machine",
        "parameters": {
            "type": "object",
            "properties": {},
            "required": []
        }
    }
]

# 连接到 MCP 服务器并调用工具
# async: 声明这是一个异步函数，可以在其中使用 await 关键词
# 异步函数允许在等待 I/O 操作时不阻塞程序，提高并发性能
async def call_mcp_tool(tool_name: str, arguments: dict) -> dict:
    server_params = StdioServerParameters(
        command="python",
        args=["mcp_server.py"],
        env=None
    )
    # async with: 异步上下文管理器，确保资源正确管理
    # 创建与 MCP 服务器的连接，自动处理连接的建立和清理
    async with stdio_client(server_params) as (read, write):
        # 创建客户端会话，用于与服务器通信
        async with ClientSession(read, write) as session:
            # await: 等待异步操作完成，在等待期间不阻塞其他代码执行
            # 初始化会话连接
            await session.initialize()
            # 调用远程工具并等待结果返回
            result = await session.call_tool(tool_name, arguments)
            # 修复结果处理
            if hasattr(result, 'content'):
                if isinstance(result.content, list) and len(result.content) > 0:
                    return json.loads(result.content[0].text)
                elif isinstance(result.content, str):
                    return json.loads(result.content)
            return {"status": "error", "error_message": "Invalid result format"}

# 与 OpenAI API 交互
# async: 声明异步函数，因为需要调用其他异步函数 (call_mcp_tool)
async def run_conversation(user_input: str) -> dict:
    messages = [{"role": "user", "content": user_input}]

    # 调用 OpenAI API (使用新的客户端API)
    response = client.chat.completions.create(
        model="gpt-3.5-turbo",
        messages=messages,
        tools=[{
            "type": "function",
            "function": functions[0]
        }],
        tool_choice="auto"
    )

    response_message = response.choices[0].message

    # 检查是否需要调用函数
    if response_message.tool_calls:
        tool_call = response_message.tool_calls[0]
        function_name = tool_call.function.name
        function_args = json.loads(tool_call.function.arguments)

        if function_name == "get_top_cpu_process":
            # await: 等待异步函数 call_mcp_tool 执行完成并返回结果
            # 这里会暂停当前函数执行，直到 MCP 工具调用完成
            result = await call_mcp_tool(function_name, function_args)
            return result
        else:
            return {"status": "error", "error_message": "Unknown function"}

    return {"status": "success", "message": response_message.content}

# 示例使用
# async: 主函数也需要声明为异步，因为要调用异步函数
async def main():
    user_input = "Find the process using the most CPU on my computer"
    # await: 等待异步对话函数完成，获取最终结果
    result = await run_conversation(user_input)
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    # asyncio.run(): 运行异步主函数的入口点
    # 它会创建事件循环并执行异步代码，直到完成
    asyncio.run(main())
