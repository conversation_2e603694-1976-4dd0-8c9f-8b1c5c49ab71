#!/usr/bin/env python3
"""
简化的 MCP 客户端测试，直接调用工具而不通过 OpenAI
"""
import json
import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def call_mcp_tool(tool_name: str, arguments: dict) -> dict:
    """连接到 MCP 服务器并调用工具"""
    server_params = StdioServerParameters(
        command="python",
        args=["mcp_server.py"],
        env=None
    )
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            result = await session.call_tool(tool_name, arguments)
            # 修复结果处理
            if hasattr(result, 'content'):
                if isinstance(result.content, list) and len(result.content) > 0:
                    return json.loads(result.content[0].text)
                elif isinstance(result.content, str):
                    return json.loads(result.content)
            return {"status": "error", "error_message": "Invalid result format"}

async def main():
    """主函数 - 模拟用户请求"""
    print("=== 模拟用户请求：查找 CPU 使用率最高的进程 ===")
    
    try:
        # 直接调用 MCP 工具
        result = await call_mcp_tool("get_top_cpu_process", {})
        
        print("MCP 工具调用结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 模拟处理结果
        if result.get("status") == "success":
            print(f"\n✅ 找到了 CPU 使用率最高的进程:")
            print(f"   进程名: {result['name']}")
            print(f"   进程ID: {result['pid']}")
            print(f"   CPU使用率: {result['cpu_percent']}%")
        else:
            print(f"\n❌ 调用失败: {result.get('error_message', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
