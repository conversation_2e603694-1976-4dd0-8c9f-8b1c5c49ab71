"""
分析你的代码中GIL阻塞问题
演示 cpu_intensive_analysis 函数在线程池中的实际表现
"""

import threading
import time
import statistics
import math
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor

def cpu_intensive_analysis(data):
    """你的CPU密集型分析函数 - 会被GIL阻塞"""
    thread_name = threading.current_thread().name
    print(f"[{time.strftime('%H:%M:%S')}] 开始分析 - 线程: {thread_name}")
    
    # 模拟复杂的数据分析
    values = []
    for record in data:
        if 'amount' in record:
            val = record['amount']
            # 增加计算复杂度 - 这里会被GIL阻塞！
            for _ in range(1000):
                val = math.sqrt(val * val + 1)
            values.append(val)
    
    if not values:
        return {"error": "No data to analyze"}
    
    result = {
        "count": len(values),
        "mean": statistics.mean(values),
        "median": statistics.median(values),
        "std_dev": statistics.stdev(values) if len(values) > 1 else 0,
        "processed_by_thread": thread_name,
        "timestamp": time.strftime('%H:%M:%S')
    }
    
    print(f"[{time.strftime('%H:%M:%S')}] 完成分析 - 线程: {thread_name}")
    return result

def create_test_data(user_id, num_transactions=20):
    """创建测试数据"""
    return [
        {
            "id": i,
            "user_id": user_id,
            "amount": (i * 100) % 1000,
            "type": "credit" if i % 2 == 0 else "debit"
        }
        for i in range(1, num_transactions + 1)
    ]

def test_your_batch_processing_with_threads():
    """测试你的批量处理代码在线程池中的表现"""
    print("=" * 70)
    print("测试：你的批量用户分析在线程池中的表现")
    print("=" * 70)
    
    # 创建5个用户的测试数据
    user_ids = [1, 2, 3, 4, 5]
    
    def process_single_user(user_id):
        """处理单个用户 - 模拟你的代码"""
        print(f"[{time.strftime('%H:%M:%S')}] 开始处理用户 {user_id}")
        
        # 模拟获取用户交易数据
        transactions = create_test_data(user_id)
        
        # CPU分析 - 这里会被GIL阻塞！
        analysis = cpu_intensive_analysis(transactions)
        
        print(f"[{time.strftime('%H:%M:%S')}] 完成处理用户 {user_id}")
        return {
            "user_id": user_id,
            "analysis": analysis
        }
    
    start_time = time.time()
    
    # 在线程池中并发处理所有用户
    with ThreadPoolExecutor(max_workers=5) as executor:
        print(f"[{time.strftime('%H:%M:%S')}] 提交5个用户任务到线程池...")
        futures = [executor.submit(process_single_user, uid) for uid in user_ids]
        results = [f.result() for f in futures]
    
    end_time = time.time()
    
    print(f"\n处理完成！总耗时: {end_time - start_time:.2f}秒")
    print("\n观察结果:")
    for result in results:
        analysis = result['analysis']
        print(f"用户 {result['user_id']}: 线程 {analysis['processed_by_thread']}, 完成时间 {analysis['timestamp']}")
    
    print(f"\n关键观察:")
    print(f"- 虽然使用了5个线程，但CPU分析任务基本是串行执行的")
    print(f"- 每个 cpu_intensive_analysis 函数都会阻塞其他线程的CPU计算")
    print(f"- 这就是GIL的影响：同一时间只有一个线程能执行Python字节码")

def test_with_process_pool():
    """使用进程池测试相同的任务"""
    print("\n" + "=" * 70)
    print("对比：使用进程池处理相同任务")
    print("=" * 70)
    
    user_ids = [1, 2, 3, 4, 5]
    
    def process_single_user_for_process(user_id):
        """为进程池优化的处理函数"""
        import time
        print(f"[{time.strftime('%H:%M:%S')}] 进程开始处理用户 {user_id}")
        
        transactions = create_test_data(user_id)
        analysis = cpu_intensive_analysis(transactions)
        
        print(f"[{time.strftime('%H:%M:%S')}] 进程完成处理用户 {user_id}")
        return {
            "user_id": user_id,
            "analysis": analysis
        }
    
    start_time = time.time()
    
    # 在进程池中并发处理所有用户
    with ProcessPoolExecutor(max_workers=5) as executor:
        print(f"[{time.strftime('%H:%M:%S')}] 提交5个用户任务到进程池...")
        futures = [executor.submit(process_single_user_for_process, uid) for uid in user_ids]
        results = [f.result() for f in futures]
    
    end_time = time.time()
    
    print(f"\n处理完成！总耗时: {end_time - start_time:.2f}秒")
    print("\n观察结果:")
    for result in results:
        analysis = result['analysis']
        print(f"用户 {result['user_id']}: 线程 {analysis['processed_by_thread']}, 完成时间 {analysis['timestamp']}")
    
    print(f"\n关键观察:")
    print(f"- 进程池中的任务可以真正并行执行")
    print(f"- 多个 cpu_intensive_analysis 函数同时运行，不会相互阻塞")
    print(f"- 这证明了只有多进程才能绕过GIL限制")

def demonstrate_gil_step_by_step():
    """逐步演示GIL如何阻塞你的代码"""
    print("\n" + "=" * 70)
    print("逐步演示：GIL如何阻塞你的 cpu_intensive_analysis 函数")
    print("=" * 70)
    
    def simple_cpu_task(task_id):
        """简化的CPU任务，便于观察"""
        thread_name = threading.current_thread().name
        
        for i in range(3):
            print(f"[{time.strftime('%H:%M:%S.%f')[:-3]}] 任务{task_id} 步骤{i+1} - 线程: {thread_name}")
            
            # CPU密集型计算（会被GIL阻塞）
            result = 0
            for j in range(1000000):  # 100万次循环
                result += j * j
            
            print(f"[{time.strftime('%H:%M:%S.%f')[:-3]}] 任务{task_id} 步骤{i+1} 完成 - 线程: {thread_name}")
    
    print("启动3个线程执行CPU任务...")
    
    with ThreadPoolExecutor(max_workers=3) as executor:
        futures = [executor.submit(simple_cpu_task, i+1) for i in range(3)]
        [f.result() for f in futures]
    
    print("\n观察时间戳:")
    print("- 注意各个任务的执行时间")
    print("- 你会发现它们基本是串行执行的，而不是并行")
    print("- 这就是GIL的效果：即使在线程池中，CPU任务仍然串行")

def main():
    """主函数"""
    print("分析你的代码中的GIL阻塞问题")
    print("重点：cpu_intensive_analysis 函数在线程池中的表现")
    
    # 测试你的批量处理代码
    test_your_batch_processing_with_threads()
    
    # 对比进程池
    test_with_process_pool()
    
    # 逐步演示
    demonstrate_gil_step_by_step()
    
    print("\n" + "=" * 70)
    print("结论:")
    print("1. 你的 cpu_intensive_analysis 函数确实会被GIL阻塞")
    print("2. 即使在线程池中，多个CPU分析任务也是串行执行的")
    print("3. 线程池只对I/O密集型任务有效（如数据库查询、网络请求）")
    print("4. 要真正并行执行CPU任务，必须使用进程池")
    print("5. 这就是为什么你的多实例架构是正确的选择！")
    print("=" * 70)

if __name__ == "__main__":
    main()
