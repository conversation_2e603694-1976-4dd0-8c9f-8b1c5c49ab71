"""
Python线程 vs 异步对比示例
演示为什么在I/O密集型任务中异步更有优势
"""

import asyncio
import threading
import time
import requests
import aiohttp
from concurrent.futures import ThreadPoolExecutor

# ============= 传统线程方式 =============
def fetch_url_sync(url):
    """同步方式获取URL - 会阻塞线程"""
    try:
        response = requests.get(url, timeout=5)
        return f"Thread {threading.current_thread().name}: {response.status_code}"
    except Exception as e:
        return f"Thread {threading.current_thread().name}: Error - {e}"

def thread_approach():
    """使用线程池处理多个HTTP请求"""
    urls = [
        "https://httpbin.org/delay/1",
        "https://httpbin.org/delay/1", 
        "https://httpbin.org/delay/1",
        "https://httpbin.org/delay/1",
        "https://httpbin.org/delay/1"
    ]
    
    start_time = time.time()
    
    # 使用线程池（类似Java的ExecutorService）
    with ThreadPoolExecutor(max_workers=5) as executor:
        results = list(executor.map(fetch_url_sync, urls))
    
    end_time = time.time()
    
    print("=== 线程方式结果 ===")
    for result in results:
        print(result)
    print(f"总耗时: {end_time - start_time:.2f}秒")
    print(f"使用了 {threading.active_count()} 个线程\n")

# ============= 异步方式 =============
async def fetch_url_async(session, url):
    """异步方式获取URL - 不会阻塞"""
    try:
        async with session.get(url, timeout=5) as response:
            return f"Async task: {response.status}"
    except Exception as e:
        return f"Async task: Error - {e}"

async def async_approach():
    """使用异步处理多个HTTP请求"""
    urls = [
        "https://httpbin.org/delay/1",
        "https://httpbin.org/delay/1",
        "https://httpbin.org/delay/1", 
        "https://httpbin.org/delay/1",
        "https://httpbin.org/delay/1"
    ]
    
    start_time = time.time()
    
    # 使用异步HTTP客户端
    async with aiohttp.ClientSession() as session:
        # 并发执行所有请求
        tasks = [fetch_url_async(session, url) for url in urls]
        results = await asyncio.gather(*tasks)
    
    end_time = time.time()
    
    print("=== 异步方式结果 ===")
    for result in results:
        print(result)
    print(f"总耗时: {end_time - start_time:.2f}秒")
    print(f"只使用了 1 个线程（主线程）\n")

# ============= 对比测试 =============
def compare_performance():
    """对比线程和异步的性能"""
    print("Python线程 vs 异步性能对比")
    print("=" * 50)
    
    # 测试线程方式
    thread_approach()
    
    # 测试异步方式  
    asyncio.run(async_approach())

if __name__ == "__main__":
    compare_performance()
