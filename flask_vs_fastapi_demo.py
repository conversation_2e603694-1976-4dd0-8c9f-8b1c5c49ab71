"""
Flask (传统线程) vs FastAPI (异步) 对比示例
演示在Web服务中线程和异步的区别
"""

# ============= Flask 传统方式 =============
from flask import Flask, jsonify
import requests
import time
import threading

app_flask = Flask(__name__)

@app_flask.route('/slow-endpoint')
def flask_slow_endpoint():
    """Flask端点 - 使用同步I/O"""
    start_time = time.time()
    
    # 模拟调用外部API（阻塞操作）
    try:
        response = requests.get('https://httpbin.org/delay/2', timeout=10)
        data = response.json()
    except Exception as e:
        data = {"error": str(e)}
    
    end_time = time.time()
    
    return jsonify({
        "message": "Flask response",
        "thread_id": threading.current_thread().name,
        "processing_time": f"{end_time - start_time:.2f}s",
        "data": data
    })

# ============= FastAPI 异步方式 =============
from fastapi import FastAPI
import aiohttp
import asyncio

app_fastapi = FastAPI()

@app_fastapi.get('/slow-endpoint')
async def fastapi_slow_endpoint():
    """FastAPI端点 - 使用异步I/O"""
    start_time = time.time()
    
    # 异步调用外部API（非阻塞操作）
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('https://httpbin.org/delay/2', timeout=10) as response:
                data = await response.json()
    except Exception as e:
        data = {"error": str(e)}
    
    end_time = time.time()
    
    return {
        "message": "FastAPI response", 
        "thread_id": threading.current_thread().name,
        "processing_time": f"{end_time - start_time:.2f}s",
        "data": data
    }

# ============= 性能测试脚本 =============
import concurrent.futures
import asyncio
import aiohttp

def test_flask_performance():
    """测试Flask的并发性能"""
    print("测试Flask性能（传统线程模型）...")
    
    def make_request():
        try:
            response = requests.get('http://localhost:5000/slow-endpoint', timeout=15)
            return response.status_code
        except Exception as e:
            return f"Error: {e}"
    
    start_time = time.time()
    
    # 并发发送10个请求
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(make_request) for _ in range(10)]
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    
    end_time = time.time()
    
    print(f"Flask: 10个并发请求耗时 {end_time - start_time:.2f}秒")
    print(f"成功请求数: {len([r for r in results if r == 200])}")

async def test_fastapi_performance():
    """测试FastAPI的并发性能"""
    print("测试FastAPI性能（异步模型）...")
    
    async def make_async_request(session):
        try:
            async with session.get('http://localhost:8000/slow-endpoint', timeout=15) as response:
                return response.status
        except Exception as e:
            return f"Error: {e}"
    
    start_time = time.time()
    
    # 并发发送10个请求
    async with aiohttp.ClientSession() as session:
        tasks = [make_async_request(session) for _ in range(10)]
        results = await asyncio.gather(*tasks)
    
    end_time = time.time()
    
    print(f"FastAPI: 10个并发请求耗时 {end_time - start_time:.2f}秒")
    print(f"成功请求数: {len([r for r in results if r == 200])}")

# ============= 启动说明 =============
if __name__ == "__main__":
    print("""
    运行说明：
    
    1. 启动Flask服务器：
       python -c "from flask_vs_fastapi_demo import app_flask; app_flask.run(port=5000)"
    
    2. 启动FastAPI服务器：
       pip install fastapi uvicorn
       uvicorn flask_vs_fastapi_demo:app_fastapi --port 8000
    
    3. 运行性能测试：
       python -c "from flask_vs_fastapi_demo import test_flask_performance, test_fastapi_performance; import asyncio; test_flask_performance(); asyncio.run(test_fastapi_performance())"
    
    预期结果：
    - Flask需要为每个请求创建/使用一个线程，受限于线程池大小
    - FastAPI在单线程中处理所有请求，通过事件循环实现并发
    """)
