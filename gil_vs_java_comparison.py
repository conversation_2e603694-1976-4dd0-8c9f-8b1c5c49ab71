"""
Python GIL vs Java线程模型对比
展示为什么Python需要多进程而Java不需要
"""

import threading
import multiprocessing as mp
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor

def cpu_intensive_task(n):
    """CPU密集型任务：计算素数"""
    def is_prime(num):
        if num < 2:
            return False
        for i in range(2, int(num ** 0.5) + 1):
            if num % i == 0:
                return False
        return True
    
    count = 0
    for i in range(n):
        if is_prime(i):
            count += 1
    
    return count

def io_intensive_task():
    """I/O密集型任务：模拟网络请求"""
    time.sleep(0.1)  # 模拟I/O等待
    return "IO completed"

# ============= 测试1: CPU密集型任务 =============

def test_python_threads_cpu():
    """Python多线程处理CPU密集型任务（受GIL限制）"""
    print("Python多线程 - CPU密集型任务:")
    
    start_time = time.time()
    
    # 使用4个线程计算
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = [executor.submit(cpu_intensive_task, 10000) for _ in range(4)]
        results = [f.result() for f in futures]
    
    end_time = time.time()
    print(f"  结果: {results}")
    print(f"  耗时: {end_time - start_time:.2f}秒")
    print(f"  说明: 由于GIL，4个线程实际上是串行执行的！\n")

def test_python_processes_cpu():
    """Python多进程处理CPU密集型任务（无GIL限制）"""
    print("Python多进程 - CPU密集型任务:")
    
    start_time = time.time()
    
    # 使用4个进程计算
    with ProcessPoolExecutor(max_workers=4) as executor:
        futures = [executor.submit(cpu_intensive_task, 10000) for _ in range(4)]
        results = [f.result() for f in futures]
    
    end_time = time.time()
    print(f"  结果: {results}")
    print(f"  耗时: {end_time - start_time:.2f}秒")
    print(f"  说明: 4个进程真正并行执行，速度提升明显！\n")

def test_single_process_cpu():
    """单进程串行处理CPU密集型任务"""
    print("单进程串行 - CPU密集型任务:")
    
    start_time = time.time()
    
    # 串行执行4次
    results = [cpu_intensive_task(10000) for _ in range(4)]
    
    end_time = time.time()
    print(f"  结果: {results}")
    print(f"  耗时: {end_time - start_time:.2f}秒")
    print(f"  说明: 基准测试，单线程执行时间\n")

# ============= 测试2: I/O密集型任务 =============

def test_python_threads_io():
    """Python多线程处理I/O密集型任务（GIL会释放）"""
    print("Python多线程 - I/O密集型任务:")
    
    start_time = time.time()
    
    # 使用10个线程处理I/O
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(io_intensive_task) for _ in range(10)]
        results = [f.result() for f in futures]
    
    end_time = time.time()
    print(f"  完成任务数: {len(results)}")
    print(f"  耗时: {end_time - start_time:.2f}秒")
    print(f"  说明: I/O等待时GIL会释放，多线程有效！\n")

async def async_io_task():
    """异步I/O任务"""
    await asyncio.sleep(0.1)  # 异步等待
    return "Async IO completed"

async def test_async_io():
    """异步处理I/O密集型任务"""
    print("异步I/O - I/O密集型任务:")
    
    start_time = time.time()
    
    # 并发执行10个异步任务
    tasks = [async_io_task() for _ in range(10)]
    results = await asyncio.gather(*tasks)
    
    end_time = time.time()
    print(f"  完成任务数: {len(results)}")
    print(f"  耗时: {end_time - start_time:.2f}秒")
    print(f"  说明: 异步I/O效率最高，单线程处理所有任务！\n")

# ============= 混合负载测试 =============

async def mixed_workload_single_process():
    """单进程异步处理混合负载"""
    print("单进程异步 - 混合负载:")
    
    start_time = time.time()
    
    # 尝试用异步处理CPU密集型任务（这是错误的做法）
    async def async_cpu_task():
        # 这会阻塞事件循环！
        return cpu_intensive_task(5000)
    
    # 并发执行CPU和I/O任务
    cpu_tasks = [async_cpu_task() for _ in range(2)]
    io_tasks = [async_io_task() for _ in range(5)]
    
    # 这里CPU任务会阻塞整个事件循环
    results = await asyncio.gather(*cpu_tasks, *io_tasks)
    
    end_time = time.time()
    print(f"  耗时: {end_time - start_time:.2f}秒")
    print(f"  说明: CPU任务阻塞了事件循环，异步失效！\n")

async def mixed_workload_multi_process():
    """多进程+异步处理混合负载"""
    print("多进程+异步 - 混合负载:")
    
    start_time = time.time()
    
    # 在进程池中执行CPU任务
    loop = asyncio.get_event_loop()
    with ProcessPoolExecutor(max_workers=2) as process_pool:
        cpu_tasks = [
            loop.run_in_executor(process_pool, cpu_intensive_task, 5000)
            for _ in range(2)
        ]
        
        # 异步执行I/O任务
        io_tasks = [async_io_task() for _ in range(5)]
        
        # 并发执行所有任务
        results = await asyncio.gather(*cpu_tasks, *io_tasks)
    
    end_time = time.time()
    print(f"  耗时: {end_time - start_time:.2f}秒")
    print(f"  说明: CPU任务在独立进程中执行，不阻塞异步I/O！\n")

# ============= Java对比代码（伪代码说明）=============

def explain_java_model():
    """解释Java的线程模型"""
    java_code = '''
// Java代码示例 - 真正的并行执行
public class JavaThreadExample {
    public static void main(String[] args) {
        ExecutorService executor = Executors.newFixedThreadPool(4);
        
        // 提交4个CPU密集型任务
        List<Future<Integer>> futures = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            futures.add(executor.submit(() -> {
                // 这些任务可以真正并行执行！
                return cpuIntensiveTask(10000);
            }));
        }
        
        // 获取结果
        for (Future<Integer> future : futures) {
            System.out.println("Result: " + future.get());
        }
        
        executor.shutdown();
    }
    
    private static int cpuIntensiveTask(int n) {
        // CPU密集型计算
        int count = 0;
        for (int i = 0; i < n; i++) {
            if (isPrime(i)) count++;
        }
        return count;
    }
}
'''
    
    print("Java线程模型说明:")
    print("="*50)
    print(java_code)
    print("关键差异:")
    print("1. Java没有GIL，多线程可以真正并行执行CPU任务")
    print("2. Java的线程是操作系统级别的，可以充分利用多核")
    print("3. Python的线程受GIL限制，同一时间只能有一个线程执行Python字节码")
    print("4. 因此Python需要多进程来实现真正的并行\n")

def main():
    """主测试函数"""
    print("Python GIL vs Java线程模型对比测试")
    print("="*60)
    
    # CPU密集型任务对比
    print("【CPU密集型任务对比】")
    test_single_process_cpu()
    test_python_threads_cpu()
    test_python_processes_cpu()
    
    # I/O密集型任务对比
    print("【I/O密集型任务对比】")
    test_python_threads_io()
    asyncio.run(test_async_io())
    
    # 混合负载对比
    print("【混合负载对比】")
    asyncio.run(mixed_workload_single_process())
    asyncio.run(mixed_workload_multi_process())
    
    # Java模型说明
    explain_java_model()

if __name__ == "__main__":
    main()
