# Kubernetes部署配置 - 简化可扩展服务
apiVersion: apps/v1
kind: Deployment
metadata:
  name: simple-scalable-service
  labels:
    app: simple-scalable-service
spec:
  # 初始副本数，可以根据负载动态调整
  replicas: 4
  selector:
    matchLabels:
      app: simple-scalable-service
  template:
    metadata:
      labels:
        app: simple-scalable-service
    spec:
      containers:
      - name: app
        image: simple-scalable-service:latest
        ports:
        - containerPort: 5000
        env:
        - name: PORT
          value: "5000"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"      # 0.25 CPU核心
          limits:
            memory: "512Mi"
            cpu: "500m"      # 0.5 CPU核心
        livenessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: simple-scalable-service
spec:
  selector:
    app: simple-scalable-service
  ports:
  - protocol: TCP
    port: 80
    targetPort: 5000
  type: ClusterIP

---
# 水平自动扩缩容
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: simple-scalable-service-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: simple-scalable-service
  minReplicas: 2
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60

---
# Ingress配置（可选）
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: simple-scalable-service-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "120"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "120"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "120"
spec:
  rules:
  - host: api.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: simple-scalable-service
            port:
              number: 80

---
# 数据库密钥配置
apiVersion: v1
kind: Secret
metadata:
  name: db-secret
type: Opaque
data:
  # base64编码的数据库连接字符串
  url: cG9zdGdyZXNxbDovL3VzZXI6cGFzc3dvcmRAbG9jYWxob3N0OjU0MzIvdGVzdGRi

---
# 配置映射（可选）
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
data:
  THREAD_POOL_SIZE: "20"
  DB_POOL_MIN_SIZE: "5"
  DB_POOL_MAX_SIZE: "10"
  LOG_LEVEL: "INFO"
